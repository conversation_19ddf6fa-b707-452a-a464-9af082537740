%%  清空环境变量
warning off; close all; clear; clc; tic;

%%  定义参数
num_features = 3;       % 输入特征数量 (ADL, ADY, AZXYG)
target_col = 3;         % 目标列是第3列 (AZXYG)
sequenceLength = 24;    % 输入序列的时间步长
prediction_horizon = 8; % 预测未来8个点
train_ratio = 0.8;      % 训练集比例

%% 导入数据
% 检查文件是否存在
if ~exist('data.xlsx', 'file')
    error('文件 data.xlsx 不存在');
end

% 尝试不同的读取方法
data_loaded = false;
try
    % 方法1: 使用xlsread
    res = xlsread('data.xlsx');
    if ~isempty(res)
        fprintf('使用xlsread读取成功\n');
        data_loaded = true;
    end
catch
    fprintf('xlsread失败\n');
end

if ~data_loaded
    try
        % 方法2: 使用readtable然后转换为数组
        T = readtable('data.xlsx');
        % 检查表格内容
        fprintf('表格变量名: ');
        disp(T.Properties.VariableNames);
        fprintf('表格大小: %d行%d列\n', height(T), width(T));

        % 尝试转换为数值数组
        res = table2array(T);
        % 如果转换失败，尝试只选择数值列
        if ~isnumeric(res)
            numeric_vars = varfun(@isnumeric, T, 'output', 'uniform');
            if any(numeric_vars)
                res = table2array(T(:, numeric_vars));
                fprintf('只选择数值列进行转换\n');
            else
                error('表格中没有数值列');
            end
        end

        if ~isempty(res)
            fprintf('使用readtable读取成功\n');
            data_loaded = true;
        end
    catch ME
        fprintf('readtable失败: %s\n', ME.message);
    end
end

if ~data_loaded
    try
        % 方法3: 使用readmatrix (MATLAB R2019a+)
        res = readmatrix('data.xlsx');
        if ~isempty(res)
            fprintf('使用readmatrix读取成功\n');
            data_loaded = true;
        end
    catch
        fprintf('readmatrix失败\n');
    end
end

% 如果所有方法都失败，创建示例数据
if ~data_loaded
    fprintf('所有读取方法都失败，创建示例数据进行测试\n');
    % 创建示例数据：100个时间点，3个特征（电压、电流、有功功率）
    t = (1:100)';
    voltage = 220 + 10*sin(0.1*t) + 5*randn(100,1);  % 电压
    current = 10 + 2*sin(0.1*t + pi/4) + randn(100,1);  % 电流
    power = voltage .* current / 1000 + 5*randn(100,1);  % 有功功率
    res = [voltage, current, power];
    fprintf('创建了示例数据：%d行%d列\n', size(res,1), size(res,2));
end

% 移除包含NaN的行
res = res(~any(isnan(res), 2), :);

% 检查数据维度
fprintf('数据维度: %d 行 %d 列\n', size(res, 1), size(res, 2));
if size(res, 1) > 0
    fprintf('前5行数据:\n');
    disp(res(1:min(5, size(res, 1)), :));
end

% 检查目标列是否存在
if size(res, 2) < target_col
    fprintf('警告：数据只有 %d 列，但目标列设置为第 %d 列\n', size(res, 2), target_col);
    fprintf('自动调整目标列为最后一列（第 %d 列）\n', size(res, 2));
    target_col = size(res, 2);
end

% 检查数据是否为空
if size(res, 1) == 0
    error('数据为空，请检查Excel文件内容');
end

%%  数据归一化
[data_normalized, ~] = mapminmax(res', 0, 1);
[~, ps_target] = mapminmax(res(:, target_col)', 0, 1);

%%  划分训练集和测试集
num_samples_total = size(data_normalized, 2);
split_point = floor(train_ratio * num_samples_total);

train_data = data_normalized(:, 1:split_point);
test_data = data_normalized(:, split_point+1:end);

%%  创建直接多步预测的时间序列数据
% 1. 创建训练集
num_train_sequences = size(train_data, 2) - sequenceLength - prediction_horizon + 1;
p_train = cell(num_train_sequences, 1);
t_train = zeros(num_train_sequences, prediction_horizon);
for i = 1:num_train_sequences
    p_train{i} = train_data(:, i : i + sequenceLength - 1);
    t_train(i, :) = train_data(target_col, i + sequenceLength : i + sequenceLength + prediction_horizon - 1);
end

% 2. 创建测试集
num_test_sequences = size(test_data, 2) - sequenceLength - prediction_horizon + 1;
p_test = cell(num_test_sequences, 1);
t_test = zeros(num_test_sequences, prediction_horizon);
for i = 1:num_test_sequences
    p_test{i} = test_data(:, i : i + sequenceLength - 1);
    t_test(i, :) = test_data(target_col, i + sequenceLength : i + sequenceLength + prediction_horizon - 1);
end

%%  创建网络 (CNN + BiLSTM 结构)
layers = [
    sequenceInputLayer(num_features, 'Name', 'input')
    convolution1dLayer(5, 32, 'Padding', 'causal', 'Name', 'conv1d')
    reluLayer('Name', 'relu')
    bilstmLayer(64, 'OutputMode', 'last', 'Name', 'bilstm')
    dropoutLayer(0.2, 'Name', 'dropout')
    fullyConnectedLayer(prediction_horizon, 'Name', 'fc')
    regressionLayer('Name', 'output')
];

%%  训练参数设置
options = trainingOptions('adam', ...
    'MaxEpochs', 50, ...
    'MiniBatchSize', 128, ...
    'InitialLearnRate', 0.008, ...
    'Shuffle', 'every-epoch', ...
    'Verbose', false, ...
    'Plots','training-progress');

%%  训练模型
net = trainNetwork(p_train, t_train, layers, options);

% =================================================================
% **核心修改: 新增对训练集的评估**
% =================================================================
%%  在训练集和测试集上进行评估
% 1. 评估训练集
predicted_train_normalized = predict(net, p_train);
actual_train_power = mapminmax('reverse', t_train', ps_target);
predicted_train_power = mapminmax('reverse', predicted_train_normalized', ps_target);
rmse_train_original = sqrt(mean((predicted_train_power - actual_train_power).^2, 'all'));

% 2. 评估测试集
predicted_test_normalized = predict(net, p_test);
actual_test_power = mapminmax('reverse', t_test', ps_target);
predicted_test_power = mapminmax('reverse', predicted_test_normalized', ps_target);
rmse_test_original = sqrt(mean((predicted_test_power - actual_test_power).^2, 'all'));

disp(' ');
disp('--- 模型评估结果 ---');
disp(['训练集 RMSE (前80%数据): ', num2str(rmse_train_original), ' kW']);
disp(['测试集 RMSE (后20%数据): ', num2str(rmse_test_original), ' kW']);
disp('--------------------');
disp(' ');

%%  进行未来8个点的预测
last_sequence = data_normalized(:, end-sequenceLength+1:end); 
input_for_prediction = {last_sequence};
future_8_steps_normalized = predict(net, input_for_prediction);
future_8_steps_power = mapminmax('reverse', future_8_steps_normalized', ps_target);

%%  将预测结果保存到Excel文件
try
    xlswrite('predicted_8_steps_power.xlsx', future_8_steps_power);
    fprintf('预测结果已保存至 predicted_8_steps_power.xlsx 文件\n');
catch
    % 如果Excel文件被锁定，尝试保存为CSV文件
    try
        csvwrite('predicted_8_steps_power.csv', future_8_steps_power);
        fprintf('Excel文件被锁定，预测结果已保存至 predicted_8_steps_power.csv 文件\n');
    catch
        % 如果CSV也失败，尝试保存为MAT文件
        save('predicted_8_steps_power.mat', 'future_8_steps_power');
        fprintf('预测结果已保存至 predicted_8_steps_power.mat 文件\n');
    end
end

%%  显示最终预测结果
disp('--- 未来2小时(8个点)预测结果 ---');
disp('预测的未来8个负荷有功功率值为 (kW):');
disp(future_8_steps_power');
disp('---------------------------------');

%% 绘制测试集中第一个预测序列的效果图
%figure;
%plot(actual_test_power(:,1), 'b-o', 'LineWidth', 1.5, 'MarkerFaceColor','b');
%hold on;
%plot(predicted_test_power(:,1), 'r--s', 'LineWidth', 1.5);
%title('测试集上某个样本的未来8步预测 vs 真实值');
%xlabel('预测时间步 (每15分钟)');
%ylabel('负荷有功功率 (kW)');
%legend('真实值', '预测值');
%grid on;
%xticks(1:8);

toc