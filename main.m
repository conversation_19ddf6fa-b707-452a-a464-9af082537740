%%  清空环境变量
warning off; close all; clear; clc; tic;

%%  定义参数
num_features = 3;       % 输入特征数量 (ADL, ADY, AZXYG)
target_col = 3;         % 目标列是第3列 (AZXYG)
sequenceLength = 24;    % 输入序列的时间步长
prediction_horizon = 8; % 预测未来8个点
train_ratio = 0.8;      % 训练集比例

%% 导入数据
res = xlsread('data.xlsx');
res = res(~any(isnan(res), 2), :);

%%  数据归一化
[data_normalized, ~] = mapminmax(res', 0, 1);
[~, ps_target] = mapminmax(res(:, target_col)', 0, 1);

%%  划分训练集和测试集
num_samples_total = size(data_normalized, 2);
split_point = floor(train_ratio * num_samples_total);

train_data = data_normalized(:, 1:split_point);
test_data = data_normalized(:, split_point+1:end);

%%  创建直接多步预测的时间序列数据
% 1. 创建训练集
num_train_sequences = size(train_data, 2) - sequenceLength - prediction_horizon + 1;
p_train = cell(num_train_sequences, 1);
t_train = zeros(num_train_sequences, prediction_horizon);
for i = 1:num_train_sequences
    p_train{i} = train_data(:, i : i + sequenceLength - 1);
    t_train(i, :) = train_data(target_col, i + sequenceLength : i + sequenceLength + prediction_horizon - 1);
end

% 2. 创建测试集
num_test_sequences = size(test_data, 2) - sequenceLength - prediction_horizon + 1;
p_test = cell(num_test_sequences, 1);
t_test = zeros(num_test_sequences, prediction_horizon);
for i = 1:num_test_sequences
    p_test{i} = test_data(:, i : i + sequenceLength - 1);
    t_test(i, :) = test_data(target_col, i + sequenceLength : i + sequenceLength + prediction_horizon - 1);
end

%%  创建网络 (CNN + BiLSTM 结构)
layers = [
    sequenceInputLayer(num_features, 'Name', 'input')
    convolution1dLayer(5, 32, 'Padding', 'causal', 'Name', 'conv1d')
    reluLayer('Name', 'relu')
    bilstmLayer(64, 'OutputMode', 'last', 'Name', 'bilstm')
    dropoutLayer(0.2, 'Name', 'dropout')
    fullyConnectedLayer(prediction_horizon, 'Name', 'fc')
    regressionLayer('Name', 'output')
];

%%  训练参数设置
options = trainingOptions('adam', ...
    'MaxEpochs', 50, ...
    'MiniBatchSize', 128, ...
    'InitialLearnRate', 0.008, ...
    'Shuffle', 'every-epoch', ...
    'Verbose', false, ...
    'Plots','training-progress');

%%  训练模型
net = trainNetwork(p_train, t_train, layers, options);

% =================================================================
% **核心修改: 新增对训练集的评估**
% =================================================================
%%  在训练集和测试集上进行评估
% 1. 评估训练集
predicted_train_normalized = predict(net, p_train);
actual_train_power = mapminmax('reverse', t_train', ps_target);
predicted_train_power = mapminmax('reverse', predicted_train_normalized', ps_target);
rmse_train_original = sqrt(mean((predicted_train_power - actual_train_power).^2, 'all'));

% 2. 评估测试集
predicted_test_normalized = predict(net, p_test);
actual_test_power = mapminmax('reverse', t_test', ps_target);
predicted_test_power = mapminmax('reverse', predicted_test_normalized', ps_target);
rmse_test_original = sqrt(mean((predicted_test_power - actual_test_power).^2, 'all'));

disp(' ');
disp('--- 模型评估结果 ---');
disp(['训练集 RMSE (前80%数据): ', num2str(rmse_train_original), ' kW']);
disp(['测试集 RMSE (后20%数据): ', num2str(rmse_test_original), ' kW']);
disp('--------------------');
disp(' ');

%%  进行未来8个点的预测
last_sequence = data_normalized(:, end-sequenceLength+1:end); 
input_for_prediction = {last_sequence};
future_8_steps_normalized = predict(net, input_for_prediction);
future_8_steps_power = mapminmax('reverse', future_8_steps_normalized', ps_target);

%%  将预测结果保存到Excel文件
xlswrite('predicted_8_steps_power.xlsx', future_8_steps_power);

%%  显示最终预测结果
disp('--- 未来2小时(8个点)预测结果 ---');
disp('预测的未来8个负荷有功功率值为 (kW):');
disp(future_8_steps_power'); 
disp('预测结果已保存至 predicted_8_steps_power.xlsx 文件');
disp('---------------------------------');

%% 绘制测试集中第一个预测序列的效果图
%figure;
%plot(actual_test_power(:,1), 'b-o', 'LineWidth', 1.5, 'MarkerFaceColor','b');
%hold on;
%plot(predicted_test_power(:,1), 'r--s', 'LineWidth', 1.5);
%title('测试集上某个样本的未来8步预测 vs 真实值');
%xlabel('预测时间步 (每15分钟)');
%ylabel('负荷有功功率 (kW)');
%legend('真实值', '预测值');
%grid on;
%xticks(1:8);

toc